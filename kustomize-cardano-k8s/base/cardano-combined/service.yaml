apiVersion: v1
kind: Service
metadata:
  name: cardano-combined
spec:
  type: ClusterIP
  ports:
  - port: 3001
    targetPort: 3001
    protocol: TCP
    name: cardano-node
  - port: 12788
    targetPort: 12788
    protocol: TCP
    name: ekg-metrics
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: prometheus
  selector:
    app: cardano-combined

---
# Alias service for cardano-node compatibility
apiVersion: v1
kind: Service
metadata:
  name: cardano-node
spec:
  type: ClusterIP
  ports:
  - port: 3001
    targetPort: 3001
    protocol: TCP
    name: cardano-node
  - port: 12788
    targetPort: 12788
    protocol: TCP
    name: ekg-metrics
  selector:
    app: cardano-combined

---
# Alias service for cardano-db-sync compatibility
apiVersion: v1
kind: Service
metadata:
  name: cardano-db-sync
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: prometheus
  selector:
    app: cardano-combined
